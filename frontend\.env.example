# AppExtera Website Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Application URL (used for SEO, redirects, and API calls)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Node environment
NODE_ENV=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL database connection string
# For development, you can use the local PGlite database
DATABASE_URL=postgresql://username:password@localhost:5432/appextera_dev

# =============================================================================
# AUTHENTICATION (Clerk)
# =============================================================================

# Clerk authentication keys
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here

# =============================================================================
# SECURITY & MONITORING
# =============================================================================

# Arcjet security key (optional)
ARCJET_KEY=ajkey_your_arcjet_key_here

# Sentry error monitoring (optional)
SENTRY_DSN=https://your_sentry_dsn_here
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project

# =============================================================================
# ANALYTICS & TRACKING
# =============================================================================

# PostHog analytics (optional)
NEXT_PUBLIC_POSTHOG_KEY=phc_your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# Google Analytics (optional)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# =============================================================================
# LOGGING
# =============================================================================

# Logtail logging service (optional)
LOGTAIL_SOURCE_TOKEN=your_logtail_token_here

# =============================================================================
# EMAIL & COMMUNICATION
# =============================================================================

# Email service configuration (for contact forms, newsletters)
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# SMTP configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Mailchimp for newsletter (optional)
MAILCHIMP_API_KEY=your_mailchimp_api_key
MAILCHIMP_AUDIENCE_ID=your_mailchimp_audience_id

# =============================================================================
# CRM INTEGRATION
# =============================================================================

# HubSpot CRM (optional)
HUBSPOT_API_KEY=your_hubspot_api_key

# Salesforce CRM (optional)
SALESFORCE_CLIENT_ID=your_salesforce_client_id
SALESFORCE_CLIENT_SECRET=your_salesforce_client_secret

# =============================================================================
# CONTENT & MEDIA
# =============================================================================

# Cloudinary for image optimization (optional)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Vercel bypass token for testing (optional)
VERCEL_BYPASS_TOKEN=your_vercel_bypass_token

# Storybook configuration
STORYBOOK_DISABLE_TELEMETRY=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
NEXT_PUBLIC_ENABLE_BLOG=true
NEXT_PUBLIC_ENABLE_DOCUMENTATION=true
NEXT_PUBLIC_ENABLE_NEWSLETTER=true
NEXT_PUBLIC_ENABLE_CONTACT_FORM=true
NEXT_PUBLIC_ENABLE_DEMO_REQUEST=true

# =============================================================================
# LOCALIZATION
# =============================================================================

# Default locale
NEXT_PUBLIC_DEFAULT_LOCALE=en

# Supported locales (comma-separated)
NEXT_PUBLIC_SUPPORTED_LOCALES=en,ar

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API URL (if using separate backend)
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# Rate limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# SEO & METADATA
# =============================================================================

# Site metadata
NEXT_PUBLIC_SITE_NAME=AppExtera
NEXT_PUBLIC_SITE_DESCRIPTION=Streamline Your E-commerce Management with AppExtera
NEXT_PUBLIC_SITE_KEYWORDS=e-commerce,browser extension,shopify,woocommerce,analytics,notifications

# Social media
NEXT_PUBLIC_TWITTER_HANDLE=@AppExtera
NEXT_PUBLIC_FACEBOOK_PAGE=AppExtera
NEXT_PUBLIC_LINKEDIN_PAGE=company/appextera
