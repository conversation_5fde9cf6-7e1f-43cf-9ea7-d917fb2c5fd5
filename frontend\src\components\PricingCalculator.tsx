'use client';

import { useState } from 'react';

interface PricingTier {
  id: string;
  name: string;
  basePrice: number;
  features: string[];
  popular?: boolean;
}

const pricingTiers: PricingTier[] = [
  {
    id: 'free',
    name: 'Free',
    basePrice: 0,
    features: [
      'Basic browser extension',
      'Up to 100 orders/month',
      'Email support',
      'Basic analytics'
    ]
  },
  {
    id: 'pro',
    name: 'Pro',
    basePrice: 29,
    popular: true,
    features: [
      'Advanced analytics dashboard',
      'Unlimited orders',
      'Priority support',
      'Team collaboration',
      'Custom integrations',
      'Advanced reporting'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    basePrice: 99,
    features: [
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantee',
      'Advanced security features',
      'White-label options',
      'Custom development'
    ]
  }
];

export function PricingCalculator() {
  const [selectedTier, setSelectedTier] = useState('pro');
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [teamSize, setTeamSize] = useState(5);

  const calculatePrice = (tier: PricingTier) => {
    let price = tier.basePrice;
    
    // Apply team size multiplier for Pro and Enterprise
    if (tier.id === 'pro' && teamSize > 5) {
      price += (teamSize - 5) * 5; // $5 per additional user
    } else if (tier.id === 'enterprise' && teamSize > 10) {
      price += (teamSize - 10) * 10; // $10 per additional user
    }
    
    // Apply yearly discount
    if (billingCycle === 'yearly') {
      price = Math.round(price * 12 * 0.8); // 20% discount for yearly
    }
    
    return price;
  };

  const getYearlySavings = (tier: PricingTier) => {
    const monthlyTotal = calculatePrice(tier) * 12;
    const yearlyPrice = calculatePrice({ ...tier, basePrice: tier.basePrice });
    const yearlyTotal = Math.round(yearlyPrice * 12 * 0.8);
    return monthlyTotal - yearlyTotal;
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Billing Toggle */}
      <div className="flex justify-center mb-12">
        <div className="bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setBillingCycle('monthly')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingCycle('yearly')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Yearly
            <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
              Save 20%
            </span>
          </button>
        </div>
      </div>

      {/* Team Size Selector */}
      <div className="text-center mb-12">
        <label className="block text-sm font-medium text-gray-700 mb-4">
          Team Size: {teamSize} {teamSize === 1 ? 'user' : 'users'}
        </label>
        <div className="max-w-md mx-auto">
          <input
            type="range"
            min="1"
            max="50"
            value={teamSize}
            onChange={(e) => setTeamSize(Number(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span>1</span>
            <span>25</span>
            <span>50+</span>
          </div>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {pricingTiers.map((tier) => {
          const price = calculatePrice(tier);
          const isSelected = selectedTier === tier.id;
          
          return (
            <div
              key={tier.id}
              className={`card-hover p-8 relative cursor-pointer transition-all ${
                tier.popular ? 'border-2 border-blue-500' : ''
              } ${
                isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
              }`}
              onClick={() => setSelectedTier(tier.id)}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {tier.name}
                </h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">
                    ${price}
                  </span>
                  <span className="text-gray-600 ml-2">
                    {billingCycle === 'yearly' ? '/year' : '/month'}
                  </span>
                  {billingCycle === 'yearly' && tier.basePrice > 0 && (
                    <div className="text-sm text-green-600 mt-1">
                      Save ${getYearlySavings(tier)} per year
                    </div>
                  )}
                </div>
                
                <button 
                  className={`w-full mb-8 ${
                    tier.id === 'free' 
                      ? 'btn-outline' 
                      : isSelected 
                        ? 'btn-primary' 
                        : 'btn-secondary'
                  }`}
                >
                  {tier.id === 'free' ? 'Get Started Free' : 
                   tier.id === 'enterprise' ? 'Contact Sales' : 'Start Free Trial'}
                </button>
                
                <ul className="space-y-4 text-left">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          );
        })}
      </div>

      {/* Pricing Notes */}
      <div className="mt-12 text-center">
        <div className="bg-gray-50 rounded-lg p-6">
          <h4 className="font-semibold text-gray-900 mb-2">Pricing Notes</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Pro plan includes 5 users, additional users $5/month each</li>
            <li>• Enterprise plan includes 10 users, additional users $10/month each</li>
            <li>• All plans include 30-day money-back guarantee</li>
            <li>• Yearly plans are billed annually with 20% discount</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
