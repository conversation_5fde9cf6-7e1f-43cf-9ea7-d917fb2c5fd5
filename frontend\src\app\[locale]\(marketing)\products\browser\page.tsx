import { getTranslations, setRequestLocale } from 'next-intl/server';

type IBrowserProductsProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IBrowserProductsProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'BrowserProducts',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function BrowserProducts(props: IBrowserProductsProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'BrowserProducts',
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      {/* Hero Section */}
      <section className="relative py-16 lg:py-24 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-purple-100 text-purple-800 text-sm font-medium mb-6">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {t('hero_badge')}
            </div>
            <h1 className="text-hero text-gradient mb-6 animate-fade-in-up">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 mb-8 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
              {t('hero_description')}
            </p>
            
            {/* Hero Stats */}
            <div className="flex flex-col sm:flex-row gap-8 justify-center mb-12 animate-fade-in-up" style={{ animationDelay: '0.15s' }}>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">{t('hero_stats_users')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_users_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{t('hero_stats_time_saved')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_time_saved_label')}</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">{t('hero_stats_browsers')}</div>
                <div className="text-sm text-gray-600">{t('hero_stats_browsers_label')}</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <button className="btn-gradient text-lg px-8 py-4">
                {t('install_free')}
              </button>
              <button className="btn-outline text-lg px-8 py-4">
                {t('view_demo')}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('products_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Productivity Suite */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-purple-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-purple-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('productivity_suite_title')}</h3>
                  <div className="text-sm text-purple-600 font-medium">{t('productivity_suite_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('productivity_suite_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('productivity_suite_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('productivity_suite_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('productivity_suite_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('productivity_suite_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('install_now')}</button>
              </div>
            </div>

            {/* Shopping Assistant */}
            <div className="card-hover p-8 group">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-600 transition-all duration-300">
                  <svg className="w-6 h-6 text-blue-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900">{t('shopping_assistant_title')}</h3>
                  <div className="text-sm text-blue-600 font-medium">{t('shopping_assistant_badge')}</div>
                </div>
              </div>
              <p className="text-gray-600 mb-6">{t('shopping_assistant_description')}</p>
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('shopping_assistant_feature_1')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('shopping_assistant_feature_2')}</span>
                </div>
                <div className="flex items-center text-sm">
                  <svg className="w-4 h-4 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span>{t('shopping_assistant_feature_3')}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-gray-900">{t('shopping_assistant_price')}</div>
                <button className="btn-gradient px-6 py-2">{t('install_now')}</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Browser Compatibility */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('compatibility_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('compatibility_description')}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                <span className="text-2xl font-bold text-blue-600">C</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('browser_chrome')}</div>
              <div className="text-xs text-gray-600">{t('browser_chrome_version')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                <span className="text-2xl font-bold text-orange-600">F</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('browser_firefox')}</div>
              <div className="text-xs text-gray-600">{t('browser_firefox_version')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                <span className="text-2xl font-bold text-blue-500">E</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('browser_edge')}</div>
              <div className="text-xs text-gray-600">{t('browser_edge_version')}</div>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white rounded-xl flex items-center justify-center mx-auto mb-4 shadow-sm">
                <span className="text-2xl font-bold text-gray-600">S</span>
              </div>
              <div className="text-sm font-medium text-gray-900">{t('browser_safari')}</div>
              <div className="text-xs text-gray-600">{t('browser_safari_version')}</div>
            </div>
          </div>
        </div>
      </section>

      {/* Privacy & Security */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('privacy_title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('privacy_description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('privacy_feature_1_title')}</h3>
              <p className="text-gray-600">{t('privacy_feature_1_description')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('privacy_feature_2_title')}</h3>
              <p className="text-gray-600">{t('privacy_feature_2_description')}</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('privacy_feature_3_title')}</h3>
              <p className="text-gray-600">{t('privacy_feature_3_description')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--color-primary)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-section text-white mb-6">
            {t('cta_title')}
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            {t('cta_description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <button className="bg-white hover:bg-gray-50 font-medium px-8 py-4 rounded-lg transition-colors duration-200" style={{ color: 'var(--color-primary)' }}>
              {t('install_extensions')}
            </button>
            <button className="border-2 border-white text-white hover:bg-white font-medium px-8 py-4 rounded-lg transition-all duration-200">
              {t('learn_more')}
            </button>
          </div>
          <p className="text-sm text-blue-200">
            ✓ {t('free_forever_guarantee')}
          </p>
        </div>
      </section>
    </div>
  );
}
