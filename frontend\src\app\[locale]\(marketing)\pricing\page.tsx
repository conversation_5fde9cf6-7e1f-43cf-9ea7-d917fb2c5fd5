import { getTranslations, setRequestLocale } from 'next-intl/server';
import { PricingCalculator } from '@/components/PricingCalculator';

type IPricingProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IPricingProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Pricing',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function Pricing(props: IPricingProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'Pricing',
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-hero text-gradient mb-6">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
              {t('hero_description')}
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Calculator */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <PricingCalculator />
      </section>

      {/* Static Pricing Cards for Reference */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              Standard Pricing Plans
            </h2>
            <p className="text-xl text-gray-600">
              Our standard pricing without customization
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Free Plan */}
            <div className="card-hover p-8 relative">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {t('free_plan_title')}
                </h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">$0</span>
                  <span className="text-gray-600 ml-2">{t('per_month')}</span>
                </div>
                <p className="text-gray-600 mb-8">
                  {t('free_plan_description')}
                </p>
                <button className="btn-outline w-full mb-8">
                  {t('get_started_free')}
                </button>
                <ul className="space-y-4 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('free_feature_1')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('free_feature_2')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('free_feature_3')}
                  </li>
                </ul>
              </div>
            </div>

            {/* Pro Plan */}
            <div className="card-hover p-8 relative border-2 border-blue-500">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                  {t('most_popular')}
                </span>
              </div>
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {t('pro_plan_title')}
                </h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">$29</span>
                  <span className="text-gray-600 ml-2">{t('per_month')}</span>
                </div>
                <p className="text-gray-600 mb-8">
                  {t('pro_plan_description')}
                </p>
                <button className="btn-primary w-full mb-8">
                  {t('start_free_trial')}
                </button>
                <ul className="space-y-4 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('pro_feature_1')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('pro_feature_2')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('pro_feature_3')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('pro_feature_4')}
                  </li>
                </ul>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="card-hover p-8 relative">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {t('enterprise_plan_title')}
                </h3>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900">$99</span>
                  <span className="text-gray-600 ml-2">{t('per_month')}</span>
                </div>
                <p className="text-gray-600 mb-8">
                  {t('enterprise_plan_description')}
                </p>
                <button className="btn-secondary w-full mb-8">
                  {t('contact_sales')}
                </button>
                <ul className="space-y-4 text-left">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('enterprise_feature_1')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('enterprise_feature_2')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('enterprise_feature_3')}
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {t('enterprise_feature_4')}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-section text-gray-900 mb-6">
              {t('faq_title')}
            </h2>
            <p className="text-xl text-gray-600">
              {t('faq_description')}
            </p>
          </div>
          <div className="space-y-8">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('faq_1_question')}
              </h3>
              <p className="text-gray-600">
                {t('faq_1_answer')}
              </p>
            </div>
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('faq_2_question')}
              </h3>
              <p className="text-gray-600">
                {t('faq_2_answer')}
              </p>
            </div>
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('faq_3_question')}
              </h3>
              <p className="text-gray-600">
                {t('faq_3_answer')}
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
