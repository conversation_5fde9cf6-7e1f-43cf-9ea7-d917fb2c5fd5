import { getTranslations, setRequestLocale } from 'next-intl/server';

type IBlogProps = {
  params: Promise<{ locale: string }>;
};

export async function generateMetadata(props: IBlogProps) {
  const { locale } = await props.params;
  const t = await getTranslations({
    locale,
    namespace: 'Blog',
  });

  return {
    title: t('meta_title'),
    description: t('meta_description'),
  };
}

export default async function Blog(props: IBlogProps) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  const t = await getTranslations({
    locale,
    namespace: 'Blog',
  });

  // Mock blog posts data - will be replaced with CMS data
  const blogPosts = [
    {
      id: 1,
      title: 'Getting Started with AppExtera Browser Extension',
      excerpt: 'Learn how to install and configure the AppExtera browser extension for your e-commerce store.',
      category: 'Tutorial',
      readTime: '5 min read',
      publishedAt: '2024-01-15',
      image: '/assets/blog/blog-1.jpg',
    },
    {
      id: 2,
      title: 'Advanced Analytics for E-commerce Success',
      excerpt: 'Discover how to leverage AppExtera\'s analytics features to boost your online store performance.',
      category: 'Analytics',
      readTime: '8 min read',
      publishedAt: '2024-01-12',
      image: '/assets/blog/blog-2.jpg',
    },
    {
      id: 3,
      title: 'Shopify Integration Best Practices',
      excerpt: 'Maximize your Shopify store efficiency with these AppExtera integration tips and tricks.',
      category: 'Integration',
      readTime: '6 min read',
      publishedAt: '2024-01-10',
      image: '/assets/blog/blog-3.jpg',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-hero text-gradient mb-6">
              {t('hero_title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
              {t('hero_description')}
            </p>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="card-hover overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="aspect-w-16 aspect-h-9 lg:aspect-none lg:h-full">
                <div className="w-full h-64 lg:h-full bg-gradient-to-br from-primary-400 to-accent-500 flex items-center justify-center">
                  <span className="text-white text-lg font-medium">Featured Image</span>
                </div>
              </div>
              <div className="p-8 lg:p-12">
                <div className="flex items-center mb-4">
                  <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                    Featured
                  </span>
                  <span className="text-gray-500 text-sm">8 min read</span>
                </div>
                <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                  {t('featured_post_title')}
                </h2>
                <p className="text-gray-600 mb-6">
                  {t('featured_post_excerpt')}
                </p>
                <button className="btn-primary">
                  {t('read_more')}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-12">
            <h2 className="text-section text-gray-900">
              {t('latest_posts')}
            </h2>
            <div className="flex space-x-4">
              <select className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                <option value="">{t('all_categories')}</option>
                <option value="tutorial">{t('tutorial')}</option>
                <option value="analytics">{t('analytics')}</option>
                <option value="integration">{t('integration')}</option>
                <option value="tips">{t('tips')}</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article key={post.id} className="card-hover overflow-hidden">
                <div className="aspect-w-16 aspect-h-9">
                  <div className="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <span className="text-gray-500 text-sm">Blog Image</span>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    <span className="bg-accent-100 text-accent-800 px-2 py-1 rounded text-xs font-medium mr-2">
                      {post.category}
                    </span>
                    <span className="text-gray-500 text-xs">{post.readTime}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(post.publishedAt).toLocaleDateString(locale)}
                    </span>
                    <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                      {t('read_more')}
                    </button>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <button className="btn-outline">
              {t('load_more_posts')}
            </button>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-primary-50">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-section text-gray-900 mb-6">
            {t('newsletter_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            {t('newsletter_description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <input
              type="email"
              placeholder={t('email_placeholder')}
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
            <button className="btn-primary whitespace-nowrap">
              {t('subscribe')}
            </button>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            {t('newsletter_disclaimer')}
          </p>
        </div>
      </section>
    </div>
  );
}
